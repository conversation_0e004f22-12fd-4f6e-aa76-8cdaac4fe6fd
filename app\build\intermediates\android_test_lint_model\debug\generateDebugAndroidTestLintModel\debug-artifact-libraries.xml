<libraries>
  <library
      name=":@@:app::debug"
      project=":app"/>
  <library
      name="androidx.navigation:navigation-common:2.7.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\3c53fbb38acea9b1debc2cdb19594cfd\transformed\navigation-common-2.7.7\jars\classes.jar"
      resolved="androidx.navigation:navigation-common:2.7.7"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\3c53fbb38acea9b1debc2cdb19594cfd\transformed\navigation-common-2.7.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-runtime:2.7.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\c4c9b20408b44b5bbd5287444844670a\transformed\navigation-runtime-2.7.7\jars\classes.jar"
      resolved="androidx.navigation:navigation-runtime:2.7.7"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\c4c9b20408b44b5bbd5287444844670a\transformed\navigation-runtime-2.7.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-common-ktx:2.7.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\062e220d2f511155d57c0395729fd85b\transformed\navigation-common-ktx-2.7.7\jars\classes.jar"
      resolved="androidx.navigation:navigation-common-ktx:2.7.7"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\062e220d2f511155d57c0395729fd85b\transformed\navigation-common-ktx-2.7.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-runtime-ktx:2.7.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\47ecf72fc3a46a445a15b7a42b645eb8\transformed\navigation-runtime-ktx-2.7.7\jars\classes.jar"
      resolved="androidx.navigation:navigation-runtime-ktx:2.7.7"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\47ecf72fc3a46a445a15b7a42b645eb8\transformed\navigation-runtime-ktx-2.7.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.navigation:navigation-compose:2.7.7@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\248ea27b6dea884c2ad29130c653a1c2\transformed\jetified-navigation-compose-2.7.7\jars\classes.jar"
      resolved="androidx.navigation:navigation-compose:2.7.7"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\248ea27b6dea884c2ad29130c653a1c2\transformed\jetified-navigation-compose-2.7.7"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\efb525bd1634e010daa81a1219619ec6\transformed\appcompat-1.6.1\jars\classes.jar"
      resolved="androidx.appcompat:appcompat:1.6.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\efb525bd1634e010daa81a1219619ec6\transformed\appcompat-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.fragment:fragment:1.3.6@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\b748c66100010346ecdbd6465a288ffd\transformed\fragment-1.3.6\jars\classes.jar"
      resolved="androidx.fragment:fragment:1.3.6"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\b748c66100010346ecdbd6465a288ffd\transformed\fragment-1.3.6"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\ba974cd29667db28a4f6cd5d0e478266\transformed\jetified-activity-1.9.0\jars\classes.jar"
      resolved="androidx.activity:activity:1.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\ba974cd29667db28a4f6cd5d0e478266\transformed\jetified-activity-1.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-ktx:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\a2cca102342aebb91d4cfa8154b50063\transformed\jetified-activity-ktx-1.9.0\jars\classes.jar"
      resolved="androidx.activity:activity-ktx:1.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\a2cca102342aebb91d4cfa8154b50063\transformed\jetified-activity-ktx-1.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity-compose:1.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\8bcc3a113bffdfc290042f29c16120f7\transformed\jetified-activity-compose-1.9.0\jars\classes.jar"
      resolved="androidx.activity:activity-compose:1.9.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\8bcc3a113bffdfc290042f29c16120f7\transformed\jetified-activity-compose-1.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material3:material3-android:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\d5ee15547fdcdb63801526e5217aa8f3\transformed\jetified-material3-release\jars\classes.jar"
      resolved="androidx.compose.material3:material3-android:1.2.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\d5ee15547fdcdb63801526e5217aa8f3\transformed\jetified-material3-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.coil-kt:coil-compose:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\ed1b04e495c6946ed0617a55837b958c\transformed\jetified-coil-compose-2.5.0\jars\classes.jar"
      resolved="io.coil-kt:coil-compose:2.5.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\ed1b04e495c6946ed0617a55837b958c\transformed\jetified-coil-compose-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.coil-kt:coil-compose-base:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\db8fce69025778c680fcc67ca094f3b8\transformed\jetified-coil-compose-base-2.5.0\jars\classes.jar"
      resolved="io.coil-kt:coil-compose-base:2.5.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\db8fce69025778c680fcc67ca094f3b8\transformed\jetified-coil-compose-base-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-icons-core-android:1.6.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\e570d92ae7a308a42a2b8e4838b408d8\transformed\jetified-material-icons-core-release\jars\classes.jar"
      resolved="androidx.compose.material:material-icons-core-android:1.6.8"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\e570d92ae7a308a42a2b8e4838b408d8\transformed\jetified-material-icons-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-icons-extended-android:1.6.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\02b487daf044b51fbe4a1080507d71c9\transformed\jetified-material-icons-extended-release\jars\classes.jar"
      resolved="androidx.compose.material:material-icons-extended-android:1.6.8"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\02b487daf044b51fbe4a1080507d71c9\transformed\jetified-material-icons-extended-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.material:material-ripple-android:1.6.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\decb354fa205037f9754b535b0d96944\transformed\jetified-material-ripple-release\jars\classes.jar"
      resolved="androidx.compose.material:material-ripple-android:1.6.8"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\decb354fa205037f9754b535b0d96944\transformed\jetified-material-ripple-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-layout-android:1.6.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\239117e25a71ba44f711e331314a04af\transformed\jetified-foundation-layout-release\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-layout-android:1.6.8"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\239117e25a71ba44f711e331314a04af\transformed\jetified-foundation-layout-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.foundation:foundation-android:1.6.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\f8a6157664e2ed3ad047df9a9309de48\transformed\jetified-foundation-release\jars\classes.jar"
      resolved="androidx.compose.foundation:foundation-android:1.6.8"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\f8a6157664e2ed3ad047df9a9309de48\transformed\jetified-foundation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-core-android:1.6.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\4c58bdc5b4c0e1372f6f18d36aa92adf\transformed\jetified-animation-core-release\jars\classes.jar"
      resolved="androidx.compose.animation:animation-core-android:1.6.8"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\4c58bdc5b4c0e1372f6f18d36aa92adf\transformed\jetified-animation-core-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.animation:animation-android:1.6.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\ed38f7020425507bf869fa7df6734d3c\transformed\jetified-animation-release\jars\classes.jar"
      resolved="androidx.compose.animation:animation-android:1.6.8"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\ed38f7020425507bf869fa7df6734d3c\transformed\jetified-animation-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-data-android:1.6.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\c961e1a13ff4696e58d44dd86f4448dd\transformed\jetified-ui-tooling-data-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-data-android:1.6.8"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\c961e1a13ff4696e58d44dd86f4448dd\transformed\jetified-ui-tooling-data-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-util-android:1.6.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\0ee7d547fec31836cfe99f06ea46036e\transformed\jetified-ui-util-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-util-android:1.6.8"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\0ee7d547fec31836cfe99f06ea46036e\transformed\jetified-ui-util-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-unit-android:1.6.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\67163f78beb5a3237c262ee4961b3259\transformed\jetified-ui-unit-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-unit-android:1.6.8"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\67163f78beb5a3237c262ee4961b3259\transformed\jetified-ui-unit-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-text-android:1.6.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\c403807fef9493f36777e79036cf47bc\transformed\jetified-ui-text-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-text-android:1.6.8"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\c403807fef9493f36777e79036cf47bc\transformed\jetified-ui-text-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-geometry-android:1.6.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\81a4ab18cff12e42bb658a4813256484\transformed\jetified-ui-geometry-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-geometry-android:1.6.8"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\81a4ab18cff12e42bb658a4813256484\transformed\jetified-ui-geometry-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-test-android:1.6.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\45cae2583facff3a49369c0ab94e5af2\transformed\jetified-ui-test-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-test-android:1.6.8"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\45cae2583facff3a49369c0ab94e5af2\transformed\jetified-ui-test-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-preview-android:1.6.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\1cbb74c5340e5b3dd63fe57cc4db50ab\transformed\jetified-ui-tooling-preview-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-preview-android:1.6.8"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\1cbb74c5340e5b3dd63fe57cc4db50ab\transformed\jetified-ui-tooling-preview-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-tooling-android:1.6.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\a1ed2ea10412ec5911322c1d3905d38b\transformed\jetified-ui-tooling-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-tooling-android:1.6.8"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\a1ed2ea10412ec5911322c1d3905d38b\transformed\jetified-ui-tooling-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-graphics-android:1.6.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\34204a23497e74268db4a2d0c7836b59\transformed\jetified-ui-graphics-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-graphics-android:1.6.8"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\34204a23497e74268db4a2d0c7836b59\transformed\jetified-ui-graphics-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\0175bc5224a1bc1f70467f11e7acf7c2\transformed\loader-1.0.0\jars\classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\0175bc5224a1bc1f70467f11e7acf7c2\transformed\loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test.espresso:espresso-core:3.5.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\b3f79873ab2316beadceacdcd755d9d9\transformed\espresso-core-3.5.1\jars\classes.jar"
      resolved="androidx.test.espresso:espresso-core:3.5.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\b3f79873ab2316beadceacdcd755d9d9\transformed\espresso-core-3.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test:core:1.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\b75c3f5ed645ec26959cbddc744a3f61\transformed\jetified-core-1.5.0\jars\classes.jar"
      resolved="androidx.test:core:1.5.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\b75c3f5ed645ec26959cbddc744a3f61\transformed\jetified-core-1.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.13.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\bcff1feb163bbd631a92445173410998\transformed\jetified-core-ktx-1.13.1\jars\classes.jar"
      resolved="androidx.core:core-ktx:1.13.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\bcff1feb163bbd631a92445173410998\transformed\jetified-core-ktx-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.appcompat:appcompat-resources:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\8592107ea41c66832d678a48639e5e93\transformed\jetified-appcompat-resources-1.6.1\jars\classes.jar"
      resolved="androidx.appcompat:appcompat-resources:1.6.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\8592107ea41c66832d678a48639e5e93\transformed\jetified-appcompat-resources-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.drawerlayout:drawerlayout:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\8c7a20fc5e7536d29ae6dc9ed576d7da\transformed\drawerlayout-1.0.0\jars\classes.jar"
      resolved="androidx.drawerlayout:drawerlayout:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\8c7a20fc5e7536d29ae6dc9ed576d7da\transformed\drawerlayout-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable-animated:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\1af169c9eea48361fa82b3ee8960ffaf\transformed\vectordrawable-animated-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable-animated:1.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\1af169c9eea48361fa82b3ee8960ffaf\transformed\vectordrawable-animated-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.vectordrawable:vectordrawable:1.1.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\755dc1d36b24e609710806085f4e5816\transformed\vectordrawable-1.1.0\jars\classes.jar"
      resolved="androidx.vectordrawable:vectordrawable:1.1.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\755dc1d36b24e609710806085f4e5816\transformed\vectordrawable-1.1.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\773049d541113e891a6a84e2b6f466d3\transformed\viewpager-1.0.0\jars\classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\773049d541113e891a6a84e2b6f466d3\transformed\viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\d6778f81ad53fdfefd8fd5a498b34c0a\transformed\customview-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\d6778f81ad53fdfefd8fd5a498b34c0a\transformed\customview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.13.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\09f65fe1ef2314bcee1397f78b5f4853\transformed\core-1.13.1\jars\classes.jar"
      resolved="androidx.core:core:1.13.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\09f65fe1ef2314bcee1397f78b5f4853\transformed\core-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.coil-kt:coil:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\19ff21bfaca98d49cb132e7426e5b83c\transformed\jetified-coil-2.5.0\jars\classes.jar"
      resolved="io.coil-kt:coil:2.5.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\19ff21bfaca98d49cb132e7426e5b83c\transformed\jetified-coil-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="io.coil-kt:coil-base:2.5.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\b1520225cbfeb84c6bd2bfa6dcde690f\transformed\jetified-coil-base-2.5.0\jars\classes.jar"
      resolved="io.coil-kt:coil-base:2.5.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\b1520225cbfeb84c6bd2bfa6dcde690f\transformed\jetified-coil-base-2.5.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\4804f76c32f406d8c47d0354422c6612\transformed\jetified-lifecycle-livedata-core-ktx-2.8.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\4804f76c32f406d8c47d0354422c6612\transformed\jetified-lifecycle-livedata-core-ktx-2.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\c1b42d5bc4d8877766b367a32b83c0fa\transformed\lifecycle-livedata-2.8.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.8.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\c1b42d5bc4d8877766b367a32b83c0fa\transformed\lifecycle-livedata-2.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-android:2.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\a2a84f767abe8bc097ef4e5ee08267c7\transformed\jetified-lifecycle-runtime-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-android:2.8.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\a2a84f767abe8bc097ef4e5ee08267c7\transformed\jetified-lifecycle-runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-jvm:2.8.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.lifecycle\lifecycle-common-jvm\2.8.0\7174a594afb73a9ad9ac9074ce78b94af3cc52a7\lifecycle-common-jvm-2.8.0.jar"
      resolved="androidx.lifecycle:lifecycle-common-jvm:2.8.0"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\889a134c82141bf775b05c470ca1df1e\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\889a134c82141bf775b05c470ca1df1e\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\e9b51627ad0f2af83507c2517d316492\transformed\lifecycle-livedata-core-2.8.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.8.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\e9b51627ad0f2af83507c2517d316492\transformed\lifecycle-livedata-core-2.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\3cac49d232b55dad9631f31aca2839f2\transformed\lifecycle-viewmodel-2.8.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.8.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\3cac49d232b55dad9631f31aca2839f2\transformed\lifecycle-viewmodel-2.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-android:2.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\3115cac7a6f7dab431be8924107540df\transformed\jetified-lifecycle-viewmodel-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-android:2.8.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\3115cac7a6f7dab431be8924107540df\transformed\jetified-lifecycle-viewmodel-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\2275b625ab8558f21eeb99c31d7d4c69\transformed\jetified-lifecycle-viewmodel-ktx-2.8.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\2275b625ab8558f21eeb99c31d7d4c69\transformed\jetified-lifecycle-viewmodel-ktx-2.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\e664fc1ffd479854a7b526d1919c4eff\transformed\jetified-lifecycle-runtime-ktx-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\e664fc1ffd479854a7b526d1919c4eff\transformed\jetified-lifecycle-runtime-ktx-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\b1e2c3f78249368c7f3b85ca2d62f742\transformed\jetified-lifecycle-viewmodel-compose-release\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\b1e2c3f78249368c7f3b85ca2d62f742\transformed\jetified-lifecycle-viewmodel-compose-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-android:1.6.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\52526fea872169553b5cc1f91b84547e\transformed\jetified-ui-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-android:1.6.8"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\52526fea872169553b5cc1f91b84547e\transformed\jetified-ui-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-test-manifest:1.6.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\69d316e0e630f7bf5c8f1df521e5bf33\transformed\jetified-ui-test-manifest-1.6.8\jars\classes.jar"
      resolved="androidx.compose.ui:ui-test-manifest:1.6.8"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\69d316e0e630f7bf5c8f1df521e5bf33\transformed\jetified-ui-test-manifest-1.6.8"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.ui:ui-test-junit4-android:1.6.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\9f9d63470f1c16e97896c010d2ffa505\transformed\jetified-ui-test-junit4-release\jars\classes.jar"
      resolved="androidx.compose.ui:ui-test-junit4-android:1.6.8"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\9f9d63470f1c16e97896c010d2ffa505\transformed\jetified-ui-test-junit4-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test.ext:junit:1.1.5@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\e541a275524c7fbe9f2124ac52673886\transformed\jetified-junit-1.1.5\jars\classes.jar"
      resolved="androidx.test.ext:junit:1.1.5"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\e541a275524c7fbe9f2124ac52673886\transformed\jetified-junit-1.1.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-common:2.6.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.room\room-common\2.6.1\ff1b9580850a9b7eef56554e356628d225785265\room-common-2.6.1.jar"
      resolved="androidx.room:room-common:2.6.1"
      provided="true"/>
  <library
      name="androidx.room:room-runtime:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\0b16710c82a6f72ae2d2935658feac13\transformed\room-runtime-2.6.1\jars\classes.jar"
      resolved="androidx.room:room-runtime:2.6.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\0b16710c82a6f72ae2d2935658feac13\transformed\room-runtime-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.room:room-ktx:2.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\21a1b06fb33bbeebbb27a5cffe110e3d\transformed\jetified-room-ktx-2.6.1\jars\classes.jar"
      resolved="androidx.room:room-ktx:2.6.1"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\21a1b06fb33bbeebbb27a5cffe110e3d\transformed\jetified-room-ktx-2.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate-ktx:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\f61c3af3fe893f092565ea1ae375441b\transformed\jetified-savedstate-ktx-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate-ktx:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\f61c3af3fe893f092565ea1ae375441b\transformed\jetified-savedstate-ktx-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\9ba8e9938d2b1ff154b3ce50f68c1bc7\transformed\jetified-savedstate-1.2.1\jars\classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\9ba8e9938d2b1ff154b3ce50f68c1bc7\transformed\jetified-savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.6.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-serialization-core-jvm\1.6.3\6b6c17d0312ba7192893adea9d52959941d0119b\kotlinx-serialization-core-jvm-1.6.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-serialization-core-jvm:1.6.3"
      provided="true"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:1.6.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-serialization-json-jvm\1.6.3\2241746853abf04073e3ab0dcd9e6729d363b313\kotlinx-serialization-json-jvm-1.6.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-serialization-json-jvm:1.6.3"
      provided="true"/>
  <library
      name="androidx.compose.runtime:runtime-saveable-android:1.6.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\713de8dc9b188d84ae72e5623beff519\transformed\jetified-runtime-saveable-release\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-saveable-android:1.6.8"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\713de8dc9b188d84ae72e5623beff519\transformed\jetified-runtime-saveable-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.compose.runtime:runtime-android:1.6.8@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\45b72766539306996952b7b87322586c\transformed\jetified-runtime-release\jars\classes.jar"
      resolved="androidx.compose.runtime:runtime-android:1.6.8"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\45b72766539306996952b7b87322586c\transformed\jetified-runtime-release"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-test-jvm:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-test-jvm\1.7.3\89e34400f452dab68fbb3caa66d854c89aaafa07\kotlinx-coroutines-test-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-test-jvm:1.7.3"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-core-jvm\1.7.3\2b09627576f0989a436a00a4a54b55fa5026fb86\kotlinx-coroutines-core-jvm-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.3"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlinx\kotlinx-coroutines-android\1.7.3\38d9cad3a0b03a10453b56577984bdeb48edeed5\kotlinx-coroutines-android-1.7.3.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3"/>
  <library
      name="com.squareup.okhttp3:okhttp:4.12.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okhttp3\okhttp\4.12.0\2f4525d4a200e97e1b87449c2cd9bd2e25b7e8cd\okhttp-4.12.0.jar"
      resolved="com.squareup.okhttp3:okhttp:4.12.0"
      provided="true"/>
  <library
      name="com.squareup.okio:okio-jvm:3.6.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup.okio\okio-jvm\3.6.0\5600569133b7bdefe1daf9ec7f4abeb6d13e1786\okio-jvm-3.6.0.jar"
      resolved="com.squareup.okio:okio-jvm:3.6.0"
      provided="true"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk8\1.9.10\c7510d64a83411a649c76f2778304ddf71d7437b\kotlin-stdlib-jdk8-1.9.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.9.10"/>
  <library
      name="androidx.test:runner:1.5.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\e9aa3bcaf74968bcf8085ae3430cc6b2\transformed\runner-1.5.2\jars\classes.jar"
      resolved="androidx.test:runner:1.5.2"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\e9aa3bcaf74968bcf8085ae3430cc6b2\transformed\runner-1.5.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test.services:storage:1.4.2@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\2e12da5302a106385af76614d9aee3d1\transformed\jetified-storage-1.4.2\jars\classes.jar"
      resolved="androidx.test.services:storage:1.4.2"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\2e12da5302a106385af76614d9aee3d1\transformed\jetified-storage-1.4.2"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test:monitor:1.6.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\03df6827c4f47cab95c6d1a7b67bb330\transformed\monitor-1.6.1\jars\classes.jar"
      resolved="androidx.test:monitor:1.6.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\03df6827c4f47cab95c6d1a7b67bb330\transformed\monitor-1.6.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.test:annotation:1.0.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\9a64296ec1e93497bf32ec423a8af670\transformed\jetified-annotation-1.0.1\jars\classes.jar"
      resolved="androidx.test:annotation:1.0.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\9a64296ec1e93497bf32ec423a8af670\transformed\jetified-annotation-1.0.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.cursoradapter:cursoradapter:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\524b95cbfeb25d1fda236de97da0339e\transformed\cursoradapter-1.0.0\jars\classes.jar"
      resolved="androidx.cursoradapter:cursoradapter:1.0.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\524b95cbfeb25d1fda236de97da0339e\transformed\cursoradapter-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.sqlite:sqlite-framework:2.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\a087163be44e4c8f599663e6bac8c759\transformed\sqlite-framework-2.4.0\jars\classes.jar"
      resolved="androidx.sqlite:sqlite-framework:2.4.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\a087163be44e4c8f599663e6bac8c759\transformed\sqlite-framework-2.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.sqlite:sqlite:2.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\be7a1df6ce6b46370a0c305713117ccf\transformed\sqlite-2.4.0\jars\classes.jar"
      resolved="androidx.sqlite:sqlite:2.4.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\be7a1df6ce6b46370a0c305713117ccf\transformed\sqlite-2.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.concurrent\concurrent-futures\1.1.0\50b7fb98350d5f42a4e49704b03278542293ba48\concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\12552b9bb492b4f4a1f0dd1b16ec9f5e\transformed\versionedparcelable-1.1.1\jars\classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\12552b9bb492b4f4a1f0dd1b16ec9f5e\transformed\versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\8084684615f38149fa91de2df48ba448\transformed\interpolator-1.0.0\jars\classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\8084684615f38149fa91de2df48ba448\transformed\interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection-jvm:1.4.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-jvm\1.4.0\e209fb7bd1183032f55a0408121c6251a81acb49\collection-jvm-1.4.0.jar"
      resolved="androidx.collection:collection-jvm:1.4.0"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\c09f54c8c37f5e1c3c5e206124715f90\transformed\core-runtime-2.2.0\jars\classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\c09f54c8c37f5e1c3c5e206124715f90\transformed\core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.arch.core\core-common\2.2.0\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="androidx.annotation:annotation-jvm:1.9.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.annotation\annotation-jvm\1.9.1\b17951747e38bf3986a24431b9ba0d039958aa5f\annotation-jvm-1.9.1.jar"
      resolved="androidx.annotation:annotation-jvm:1.9.1"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\690bd297cfe8b98887687974246b2887\transformed\jetified-annotation-experimental-1.4.0\jars\classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\690bd297cfe8b98887687974246b2887\transformed\jetified-annotation-experimental-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib-jdk7\1.9.10\bc5bfc2690338defd5195b05c57562f2194eeb10\kotlin-stdlib-jdk7-1.9.10.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.9.10"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:1.9.24@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains.kotlin\kotlin-stdlib\1.9.24\9928532f12c66ad816a625b3f9984f8368ca6d2b\kotlin-stdlib-1.9.24.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:1.9.24"/>
  <library
      name="com.github.mik3y:usb-serial-for-android:3.9.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\17419a4598f67dbf1d489f9e00a17f73\transformed\jetified-usb-serial-for-android-3.9.0\jars\classes.jar"
      resolved="com.github.mik3y:usb-serial-for-android:3.9.0"
      provided="true"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\17419a4598f67dbf1d489f9e00a17f73\transformed\jetified-usb-serial-for-android-3.9.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="junit:junit:4.13.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\junit\junit\4.13.2\8ac9e16d933b6fb43bc7f576336b8f4d7eb5ba12\junit-4.13.2.jar"
      resolved="junit:junit:4.13.2"/>
  <library
      name="org.hamcrest:hamcrest-integration:1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.hamcrest\hamcrest-integration\1.3\5de0c73fef18917cd85d0ab70bb23818685e4dfd\hamcrest-integration-1.3.jar"
      resolved="org.hamcrest:hamcrest-integration:1.3"/>
  <library
      name="org.hamcrest:hamcrest-library:1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.hamcrest\hamcrest-library\1.3\4785a3c21320980282f9f33d0d1264a69040538f\hamcrest-library-1.3.jar"
      resolved="org.hamcrest:hamcrest-library:1.3"/>
  <library
      name="org.hamcrest:hamcrest-core:1.3@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.hamcrest\hamcrest-core\1.3\42a25dc3219429f0e5d060061f71acb49bf010a0\hamcrest-core-1.3.jar"
      resolved="org.hamcrest:hamcrest-core:1.3"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\23.0.0\8cc20c07506ec18e0834947b84a864bfc094484e\annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="androidx.tracing:tracing:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\937bd6d22cde3bf766e601ae2f47f22f\transformed\jetified-tracing-1.0.0\jars\classes.jar"
      resolved="androidx.tracing:tracing:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\937bd6d22cde3bf766e601ae2f47f22f\transformed\jetified-tracing-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.code.findbugs:jsr305:2.0.2@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.findbugs\jsr305\2.0.2\516c03b21d50a644d538de0f0369c620989cd8f0\jsr305-2.0.2.jar"
      resolved="com.google.code.findbugs:jsr305:2.0.2"/>
  <library
      name="com.google.guava:listenablefuture:1.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\1.0\c949a840a6acbc5268d088e47b04177bf90b3cad\listenablefuture-1.0.jar"
      resolved="com.google.guava:listenablefuture:1.0"/>
  <library
      name="androidx.test.espresso:espresso-idling-resource:3.5.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\539a426b6a0855d946816e8b5e79f42e\transformed\espresso-idling-resource-3.5.1\jars\classes.jar"
      resolved="androidx.test.espresso:espresso-idling-resource:3.5.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\539a426b6a0855d946816e8b5e79f42e\transformed\espresso-idling-resource-3.5.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.squareup:javawriter:2.1.1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.squareup\javawriter\2.1.1\67ff45d9ae02e583d0f9b3432a5ebbe05c30c966\javawriter-2.1.1.jar"
      resolved="com.squareup:javawriter:2.1.1"/>
  <library
      name="javax.inject:javax.inject:1@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\javax.inject\javax.inject\1\6975da39a7040257bd51d21a231b76c915872d38\javax.inject-1.jar"
      resolved="javax.inject:javax.inject:1"/>
  <library
      name="androidx.emoji2:emoji2:1.3.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\3542d432e7840edb837f5dea0e9e8e7b\transformed\jetified-emoji2-1.3.0\jars\classes.jar;C:\Users\<USER>\.gradle\caches\8.12\transforms\3542d432e7840edb837f5dea0e9e8e7b\transformed\jetified-emoji2-1.3.0\jars\libs\repackaged.jar"
      resolved="androidx.emoji2:emoji2:1.3.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\3542d432e7840edb837f5dea0e9e8e7b\transformed\jetified-emoji2-1.3.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.8.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\900bb11fd0038ca5ae0ef63ddd4fcd6e\transformed\jetified-lifecycle-process-2.8.0\jars\classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.8.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\900bb11fd0038ca5ae0ef63ddd4fcd6e\transformed\jetified-lifecycle-process-2.8.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview-poolingcontainer:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\658e1dfae8fc4b0d3065ed143caa670a\transformed\jetified-customview-poolingcontainer-1.0.0\jars\classes.jar"
      resolved="androidx.customview:customview-poolingcontainer:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\658e1dfae8fc4b0d3065ed143caa670a\transformed\jetified-customview-poolingcontainer-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.autofill:autofill:1.0.0@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\5f2a9cda2a6dd8eb89d0a2b632e836a9\transformed\jetified-autofill-1.0.0\jars\classes.jar"
      resolved="androidx.autofill:autofill:1.0.0"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\5f2a9cda2a6dd8eb89d0a2b632e836a9\transformed\jetified-autofill-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.3.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\b2b007447d6efa8f862dbc29aa895b6a\transformed\jetified-profileinstaller-1.3.1\jars\classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.3.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\b2b007447d6efa8f862dbc29aa895b6a\transformed\jetified-profileinstaller-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="C:\Users\<USER>\.gradle\caches\8.12\transforms\6aadd1cb1ac3ab76b0e2e4ff1bb6fb56\transformed\jetified-startup-runtime-1.1.1\jars\classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="C:\Users\<USER>\.gradle\caches\8.12\transforms\6aadd1cb1ac3ab76b0e2e4ff1bb6fb56\transformed\jetified-startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection-ktx:1.4.0@jar"
      jars="C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\androidx.collection\collection-ktx\1.4.0\2ad14aed781c4a73ed4dbb421966d408a0a06686\collection-ktx-1.4.0.jar"
      resolved="androidx.collection:collection-ktx:1.4.0"/>
</libraries>
